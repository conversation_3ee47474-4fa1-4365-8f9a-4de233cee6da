import pandas as pd
import re
import os
from typing import Dict, List

class TransactionCategorizer:
    """
    Rule-based transaction categorization system with user override support.
    Uses category_overrides.csv for user-controlled categorization rules.
    """
    
    def __init__(self, overrides_file='category_overrides.csv'):
        self.overrides_file = overrides_file
        self.user_overrides = self._load_user_overrides()
        self.default_rules = self._get_default_rules()
    
    def _load_user_overrides(self) -> Dict[str, str]:
        """Load user-defined category overrides from CSV file"""
        overrides = {}
        
        if os.path.exists(self.overrides_file):
            try:
                df = pd.read_csv(self.overrides_file)
                if 'Description' in df.columns and 'Category' in df.columns:
                    # Convert to dictionary for fast lookup
                    for _, row in df.iterrows():
                        # Use uppercase for case-insensitive matching
                        overrides[row['Description'].upper()] = row['Category']
                    print(f"Loaded {len(overrides)} user category overrides")
                else:
                    print("Warning: category_overrides.csv must have 'Description' and 'Category' columns")
            except Exception as e:
                print(f"Error loading category overrides: {e}")
        else:
            print(f"No category overrides file found at {self.overrides_file}")
            # Create example file
            self._create_example_overrides()
        
        return overrides
    
    def _create_example_overrides(self):
        """Create an example category overrides file"""
        example_data = {
            'Description': [
                'WALMART',
                'TARGET',
                'SHELL',
                'EXXON',
                'NETFLIX',
                'SPOTIFY',
                'AMAZON',
                'STARBUCKS',
                'MCDONALDS',
                'ELECTRIC COMPANY',
                'WATER DEPT',
                'INTERNET SERVICE',
                'PHONE BILL',
                'RENT PAYMENT',
                'MORTGAGE',
                'CREDIT CARD PAYMENT',
                'BANK TRANSFER',
                'PAYROLL DEPOSIT',
                'DIVIDEND',
                'INTEREST'
            ],
            'Category': [
                'Shopping',
                'Shopping',
                'Transportation',
                'Transportation',
                'Entertainment',
                'Entertainment',
                'Shopping',
                'Food & Dining',
                'Food & Dining',
                'Utilities',
                'Utilities',
                'Utilities',
                'Utilities',
                'Housing',
                'Housing',
                'Financial',
                'Financial',
                'Income',
                'Income',
                'Income'
            ]
        }
        
        try:
            df = pd.DataFrame(example_data)
            df.to_csv(self.overrides_file, index=False)
            print(f"Created example category overrides file: {self.overrides_file}")
        except Exception as e:
            print(f"Error creating example overrides file: {e}")
    
    def _get_default_rules(self) -> Dict[str, List[str]]:
        """Define default categorization rules"""
        return {
            'Food & Dining': [
                'restaurant', 'mcdonald', 'burger', 'pizza', 'starbucks', 'coffee',
                'dining', 'food', 'cafe', 'deli', 'bakery', 'bar', 'pub', 'grill',
                'kitchen', 'bistro', 'eatery', 'subway', 'taco', 'kfc', 'wendy',
                'domino', 'papa', 'dunkin', 'chipotle', 'panera', 'chick-fil-a'
            ],
            'Transportation': [
                'gas', 'fuel', 'shell', 'exxon', 'bp', 'chevron', 'mobil', 'citgo',
                'parking', 'uber', 'lyft', 'taxi', 'metro', 'bus', 'train', 'airline',
                'flight', 'car wash', 'auto', 'repair', 'mechanic', 'oil change',
                'tire', 'dmv', 'registration', 'insurance'
            ],
            'Shopping': [
                'walmart', 'target', 'amazon', 'ebay', 'costco', 'sams club',
                'home depot', 'lowes', 'best buy', 'apple store', 'mall',
                'department', 'retail', 'store', 'shop', 'market', 'grocery',
                'supermarket', 'pharmacy', 'cvs', 'walgreens', 'rite aid'
            ],
            'Utilities': [
                'electric', 'electricity', 'power', 'gas company', 'water',
                'sewer', 'trash', 'garbage', 'internet', 'cable', 'phone',
                'wireless', 'verizon', 'att', 'tmobile', 'sprint', 'comcast',
                'xfinity', 'spectrum', 'utility'
            ],
            'Entertainment': [
                'netflix', 'hulu', 'disney', 'spotify', 'apple music', 'youtube',
                'gaming', 'steam', 'xbox', 'playstation', 'movie', 'theater',
                'cinema', 'concert', 'show', 'event', 'ticket', 'entertainment',
                'subscription', 'streaming'
            ],
            'Healthcare': [
                'doctor', 'hospital', 'medical', 'pharmacy', 'dental', 'dentist',
                'clinic', 'health', 'medicine', 'prescription', 'copay',
                'insurance', 'medicare', 'medicaid', 'urgent care'
            ],
            'Financial': [
                'bank', 'credit card', 'loan', 'mortgage', 'payment', 'transfer',
                'fee', 'charge', 'interest', 'atm', 'withdrawal', 'deposit',
                'investment', 'trading', 'brokerage', 'financial'
            ],
            'Housing': [
                'rent', 'mortgage', 'property', 'real estate', 'homeowners',
                'hoa', 'maintenance', 'repair', 'improvement', 'furniture',
                'appliance', 'cleaning', 'lawn', 'garden'
            ],
            'Income': [
                'payroll', 'salary', 'wage', 'bonus', 'commission', 'dividend',
                'interest', 'refund', 'rebate', 'cashback', 'deposit',
                'direct deposit', 'income', 'earnings'
            ]
        }
    
    def categorize_transaction(self, description: str, memo: str = '') -> str:
        """Categorize a single transaction"""
        # Combine description and memo for analysis
        full_text = f"{description} {memo}".upper().strip()
        
        # First check user overrides (exact match)
        for override_desc, category in self.user_overrides.items():
            if override_desc in full_text:
                return category
        
        # Then check default rules (keyword matching)
        for category, keywords in self.default_rules.items():
            for keyword in keywords:
                if keyword.upper() in full_text:
                    return category
        
        # Default category for unmatched transactions
        return 'Other'
    
    def categorize_transactions(self, df: pd.DataFrame) -> pd.DataFrame:
        """Categorize all transactions in a DataFrame"""
        df = df.copy()
        
        # Apply categorization
        df['Category'] = df.apply(
            lambda row: self.categorize_transaction(
                str(row.get('Description', '')), 
                str(row.get('Memo', ''))
            ), 
            axis=1
        )
        
        # Print categorization summary
        category_counts = df['Category'].value_counts()
        print("\nTransaction Categorization Summary:")
        print("-" * 40)
        for category, count in category_counts.items():
            print(f"{category}: {count} transactions")
        
        return df
    
    def get_category_rules(self) -> Dict:
        """Get all categorization rules for debugging/display"""
        return {
            'user_overrides': self.user_overrides,
            'default_rules': self.default_rules
        }
    
    def add_override(self, description: str, category: str):
        """Add a new user override and save to file"""
        self.user_overrides[description.upper()] = category
        
        # Update the CSV file
        try:
            # Read existing overrides
            if os.path.exists(self.overrides_file):
                df = pd.read_csv(self.overrides_file)
            else:
                df = pd.DataFrame(columns=['Description', 'Category'])
            
            # Add new override
            new_row = pd.DataFrame({'Description': [description], 'Category': [category]})
            df = pd.concat([df, new_row], ignore_index=True)
            
            # Remove duplicates (keep last)
            df = df.drop_duplicates(subset=['Description'], keep='last')
            
            # Save back to file
            df.to_csv(self.overrides_file, index=False)
            print(f"Added override: {description} -> {category}")
            
        except Exception as e:
            print(f"Error saving override: {e}")
