# AI Budget App

An intelligent budgeting application that uses AI and machine learning to analyze personal financial data, categorize transactions, and provide personalized insights for better financial management.

## Features

### **AI-Powered Intelligence**
- **Smart Transaction Categorization**: Automatically categorizes transactions using rule-based system with user overrides
- **Spending Pattern Analysis**: Identifies trends and unusual spending behavior
- **Predictive Insights**: SARIMA forecasting for future spending based on historical data
- **Anomaly Detection**: Isolation Forest model to detect unusual transactions and potential fraud
- **Personalized Recommendations**: Suggests budget optimizations and savings opportunities

### **Advanced Analytics**
- Category-wise spending breakdown with pie chart visualization
- Monthly spending trends
- 30-day spending forecasts
- Real-time anomaly detection alerts

### **Data Integration**
- CSV bank data import and processing
- Real-time transaction processing
- Plaid API integration for live bank connections
- Support for multiple account types

### **Modern Web Interface**
- Responsive Flask-based backend
- Interactive HTML/JavaScript frontend
- Beautiful data visualizations with charts
- Real-time dashboard updates

## Quick Start

### Prerequisites
- Python 3.8+
- Virtual environment (recommended)
- Modern web browser

### Installation

1. **Set up Python virtual environment**
   ```bash
   python -m venv venv
   
   # On Windows
   venv\Scripts\activate
   
   # On macOS/Linux
   source venv/bin/activate
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Prepare your data**
   - Place your bank CSV file as `your_bank_data.csv` in the root directory
   - Ensure CSV has columns: Date, Description, Memo, Debit, Credit, Balance
   - Optionally create `category_overrides.csv` for custom categorization rules

4. **Configure Plaid (Optional)**
   - Create `.env` file with your Plaid credentials
   - Add PLAID_CLIENT_ID, PLAID_SECRET, PLAID_ENV

5. **Start the application**
   ```bash
   python app.py
   ```
   Application will be available at: http://localhost:5000

## Project Structure

```
AI_BUDGET_APP/
├── app.py                    # Main Flask application
├── models/                   # ML models and data processing
│   ├── categorizer.py       # Transaction categorization
│   ├── forecaster.py        # SARIMA forecasting model
│   └── anomaly_detector.py  # Isolation Forest anomaly detection
├── static/                   # Frontend assets
│   ├── css/
│   ├── js/
│   └── images/
├── templates/               # HTML templates
│   └── index.html          # Main dashboard
├── data/                   # Data files
│   ├── your_bank_data.csv  # Bank transaction data
│   └── category_overrides.csv # User categorization overrides
├── requirements.txt        # Python dependencies
├── .env                   # Environment variables (create this)
└── README.md             # This file
```

## API Endpoints

### Core Endpoints
- `GET /` - Main dashboard
- `GET /api/health` - Health check
- `GET /api/transactions` - Get all transactions
- `GET /api/categories` - Get spending by category
- `GET /api/forecast` - Get 30-day spending forecast
- `GET /api/anomalies` - Get detected anomalies

### Plaid Integration
- `POST /api/plaid/link_token` - Create Plaid link token
- `POST /api/plaid/exchange_token` - Exchange public token
- `GET /api/plaid/accounts` - Get linked accounts
- `GET /api/plaid/transactions` - Get transactions from Plaid

## Technology Stack

### Backend
- **Flask**: Web framework
- **Pandas**: Data processing and analysis
- **Scikit-learn**: Machine learning (Isolation Forest)
- **Statsmodels**: Time series forecasting (SARIMA)
- **Plaid API**: Bank data integration

### Frontend
- **HTML5/CSS3**: Modern web standards
- **JavaScript**: Interactive functionality
- **Chart.js**: Data visualization
- **Bootstrap**: Responsive UI framework

## Data Format

### CSV Requirements
Your bank CSV should have these columns:
```csv
Date,Description,Memo,Debit,Credit,Balance
07-07-25,Electronic Withdrawal CAPITAL,ONE  - MOBILE PMT,-617.42,,1329.4
07-06-25,Deposit ck gas,,,41,2327.32
```

### Category Override Format
```csv
Description,Category
WALMART,Shopping
SHELL,Transportation
NETFLIX,Entertainment
```

---

**Built with ❤️ for better financial management**
