from flask import Flask, render_template, jsonify, request
from flask_cors import CORS
import pandas as pd
import numpy as np
import os
from datetime import datetime, timedelta
import json
from dotenv import load_dotenv

# Import our custom modules
from models.categorizer import TransactionCategorizer
from models.forecaster import SpendingForecaster
from models.anomaly_detector import AnomalyDetector

# Plaid imports
try:
    import plaid
    from plaid.api import plaid_api
    from plaid.model.transactions_get_request import TransactionsGetRequest
    from plaid.model.link_token_create_request import LinkTokenCreateRequest
    from plaid.model.link_token_create_request_user import LinkTokenCreateRequestUser
    from plaid.model.item_public_token_exchange_request import ItemPublicTokenExchangeRequest
    from plaid.model.accounts_get_request import AccountsGetRequest
    from plaid.model.country_code import CountryCode
    from plaid.model.products import Products
    from plaid.configuration import Configuration
    from plaid.api_client import ApiClient
    PLAID_AVAILABLE = True
except ImportError:
    print("Plaid SDK not available. Install with: pip install plaid-python")
    PLAID_AVAILABLE = False

# Load environment variables
load_dotenv()

app = Flask(__name__)
CORS(app)
app.secret_key = os.getenv('SECRET_KEY', 'your-secret-key-here')

# Initialize our AI models
categorizer = TransactionCategorizer()
forecaster = SpendingForecaster()
anomaly_detector = AnomalyDetector()

# Initialize Plaid client if available
plaid_client = None
if PLAID_AVAILABLE:
    try:
        # Use the correct environment mapping for newer Plaid SDK
        env_mapping = {
            'sandbox': plaid.Environment.sandbox,
            'development': plaid.Environment.development,
            'production': plaid.Environment.production
        }

        plaid_env = os.getenv('PLAID_ENV', 'sandbox')
        configuration = Configuration(
            host=env_mapping.get(plaid_env, plaid.Environment.sandbox),
            api_key={
                'clientId': os.getenv('PLAID_CLIENT_ID'),
                'secret': os.getenv('PLAID_SECRET')
            }
        )
        api_client = ApiClient(configuration)
        plaid_client = plaid_api.PlaidApi(api_client)
        print("Plaid client initialized successfully")
    except Exception as e:
        print(f"Failed to initialize Plaid client: {e}")
        plaid_client = None

# Global variables to store data
processed_data = None
plaid_access_token = None

def load_and_process_data():
    """Load and process the bank data"""
    global processed_data
    
    data_file = os.getenv('DATA_FILE', 'your_bank_data.csv')
    
    if not os.path.exists(data_file):
        return None
    
    try:
        # Load the CSV data
        df = pd.read_csv(data_file)
        
        # Clean and process the data
        df = clean_transaction_data(df)
        
        # Categorize transactions
        df = categorizer.categorize_transactions(df)
        
        # Detect anomalies
        df = anomaly_detector.detect_anomalies(df)
        
        processed_data = df
        return df
        
    except Exception as e:
        print(f"Error loading data: {e}")
        return None

def clean_transaction_data(df):
    """Clean and standardize transaction data"""
    # Convert date column
    df['Date'] = pd.to_datetime(df['Date'], errors='coerce')
    
    # Handle missing values
    df['Debit'] = pd.to_numeric(df['Debit'], errors='coerce').fillna(0)
    df['Credit'] = pd.to_numeric(df['Credit'], errors='coerce').fillna(0)
    df['Balance'] = pd.to_numeric(df['Balance'], errors='coerce')
    
    # Calculate amount (negative for debits, positive for credits)
    df['Amount'] = df['Credit'] - df['Debit']
    
    # Clean description
    df['Description'] = df['Description'].fillna('Unknown')
    df['Memo'] = df['Memo'].fillna('')
    
    # Sort by date
    df = df.sort_values('Date').reset_index(drop=True)
    
    return df

@app.route('/')
def dashboard():
    """Main dashboard page"""
    return render_template('index.html')

@app.route('/api/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'data_loaded': processed_data is not None,
        'total_transactions': len(processed_data) if processed_data is not None else 0
    })

@app.route('/api/transactions')
def get_transactions():
    """Get all transactions"""
    if processed_data is None:
        load_and_process_data()
    
    if processed_data is None:
        return jsonify({'error': 'No data available'}), 404
    
    # Convert to JSON-serializable format
    transactions = processed_data.copy()
    transactions['Date'] = transactions['Date'].dt.strftime('%Y-%m-%d')
    
    return jsonify({
        'transactions': transactions.to_dict('records'),
        'total': len(transactions)
    })

@app.route('/api/categories')
def get_categories():
    """Get spending analysis by category"""
    if processed_data is None:
        load_and_process_data()
    
    if processed_data is None:
        return jsonify({'error': 'No data available'}), 404
    
    # Calculate spending by category (only debits/expenses)
    expenses = processed_data[processed_data['Amount'] < 0].copy()
    expenses['Amount'] = expenses['Amount'].abs()  # Make positive for display
    
    category_spending = expenses.groupby('Category')['Amount'].sum().sort_values(ascending=False)
    
    return jsonify({
        'categories': category_spending.to_dict(),
        'total_spending': category_spending.sum(),
        'category_count': len(category_spending)
    })

@app.route('/api/forecast')
def get_forecast():
    """Get 30-day spending forecast"""
    if processed_data is None:
        load_and_process_data()
    
    if processed_data is None:
        return jsonify({'error': 'No data available'}), 404
    
    try:
        forecast_data = forecaster.generate_forecast(processed_data)
        return jsonify(forecast_data)
    except Exception as e:
        return jsonify({'error': f'Forecast generation failed: {str(e)}'}), 500

@app.route('/api/anomalies')
def get_anomalies():
    """Get detected anomalies"""
    if processed_data is None:
        load_and_process_data()
    
    if processed_data is None:
        return jsonify({'error': 'No data available'}), 404
    
    # Get anomalous transactions
    anomalies = processed_data[processed_data['is_anomaly'] == True].copy()
    anomalies['Date'] = anomalies['Date'].dt.strftime('%Y-%m-%d')
    
    return jsonify({
        'anomalies': anomalies.to_dict('records'),
        'total_anomalies': len(anomalies),
        'anomaly_rate': len(anomalies) / len(processed_data) * 100
    })

@app.route('/api/reload_data', methods=['POST'])
def reload_data():
    """Reload and reprocess all data from CSV"""
    global processed_data

    try:
        print("Reloading data from CSV...")
        processed_data = None  # Clear existing data
        load_and_process_data()  # Reload everything

        if processed_data is not None:
            return jsonify({
                'success': True,
                'message': f'Successfully reloaded {len(processed_data)} transactions',
                'transaction_count': len(processed_data)
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to load data from CSV'
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Error reloading data: {str(e)}'
        }), 500

@app.route('/api/stats')
def get_stats():
    """Get overall financial statistics"""
    if processed_data is None:
        load_and_process_data()

    if processed_data is None:
        return jsonify({'error': 'No data available'}), 404

    df = processed_data.copy()

    # Calculate key statistics
    total_income = float(df[df['Amount'] > 0]['Amount'].sum())
    total_expenses = float(abs(df[df['Amount'] < 0]['Amount'].sum()))
    net_change = total_income - total_expenses
    current_balance = float(df['Balance'].iloc[-1]) if len(df) > 0 else 0.0

    # Monthly statistics - avoid Period objects
    df['Month'] = df['Date'].dt.strftime('%Y-%m')  # Use string instead of Period
    monthly_expenses = df[df['Amount'] < 0].groupby('Month')['Amount'].sum().abs()
    avg_monthly_spending = float(monthly_expenses.mean()) if len(monthly_expenses) > 0 else 0.0

    return jsonify({
        'current_balance': current_balance,
        'total_income': total_income,
        'total_expenses': total_expenses,
        'net_change': net_change,
        'avg_monthly_spending': avg_monthly_spending,
        'transaction_count': len(df),
        'date_range': {
            'start': df['Date'].min().strftime('%Y-%m-%d') if len(df) > 0 else None,
            'end': df['Date'].max().strftime('%Y-%m-%d') if len(df) > 0 else None
        }
    })

# Plaid API endpoints
@app.route('/api/plaid/link_token', methods=['POST'])
def create_link_token():
    """Create a Plaid Link token for account linking"""
    if not PLAID_AVAILABLE or not plaid_client:
        return jsonify({'error': 'Plaid not configured'}), 400

    try:
        request_data = LinkTokenCreateRequest(
            products=[Products('transactions')],
            client_name="AI Budget App",
            country_codes=[CountryCode('US')],
            language='en',
            user=LinkTokenCreateRequestUser(client_user_id='user-1')
        )

        response = plaid_client.link_token_create(request_data)
        return jsonify({'link_token': response['link_token']})

    except Exception as e:
        return jsonify({'error': f'Failed to create link token: {str(e)}'}), 500

@app.route('/api/plaid/exchange_token', methods=['POST'])
def exchange_public_token():
    """Exchange public token for access token"""
    global plaid_access_token

    if not PLAID_AVAILABLE or not plaid_client:
        return jsonify({'error': 'Plaid not configured'}), 400

    try:
        public_token = request.json.get('public_token')
        if not public_token:
            return jsonify({'error': 'Public token required'}), 400

        exchange_request = ItemPublicTokenExchangeRequest(public_token=public_token)
        response = plaid_client.item_public_token_exchange(exchange_request)

        plaid_access_token = response['access_token']

        return jsonify({
            'success': True,
            'message': 'Account linked successfully'
        })

    except Exception as e:
        return jsonify({'error': f'Failed to exchange token: {str(e)}'}), 500

@app.route('/api/plaid/accounts')
def get_plaid_accounts():
    """Get linked Plaid accounts"""
    if not PLAID_AVAILABLE or not plaid_client or not plaid_access_token:
        return jsonify({'error': 'Plaid not configured or no account linked'}), 400

    try:
        request_data = AccountsGetRequest(access_token=plaid_access_token)
        response = plaid_client.accounts_get(request_data)

        accounts = []
        for account in response['accounts']:
            accounts.append({
                'account_id': account['account_id'],
                'name': account['name'],
                'type': account['type'],
                'subtype': account['subtype'],
                'balance': account['balances']['current']
            })

        return jsonify({'accounts': accounts})

    except Exception as e:
        return jsonify({'error': f'Failed to get accounts: {str(e)}'}), 500

@app.route('/api/plaid/transactions')
def get_plaid_transactions():
    """Get transactions from Plaid and process them"""
    global processed_data

    if not PLAID_AVAILABLE or not plaid_client or not plaid_access_token:
        return jsonify({'error': 'Plaid not configured or no account linked'}), 400

    try:
        # Get transactions from last 30 days
        start_date = datetime.now() - timedelta(days=30)
        end_date = datetime.now()

        request_data = TransactionsGetRequest(
            access_token=plaid_access_token,
            start_date=start_date.date(),
            end_date=end_date.date()
        )

        response = plaid_client.transactions_get(request_data)
        transactions = response['transactions']

        # Convert Plaid transactions to our format
        df_data = []
        for txn in transactions:
            df_data.append({
                'Date': txn['date'],
                'Description': txn['name'],
                'Memo': txn.get('merchant_name', ''),
                'Amount': -txn['amount'],  # Plaid uses positive for expenses
                'Debit': txn['amount'] if txn['amount'] > 0 else 0,
                'Credit': 0 if txn['amount'] > 0 else abs(txn['amount']),
                'Balance': 0  # We don't have running balance from Plaid
            })

        if df_data:
            df = pd.DataFrame(df_data)
            df = clean_transaction_data(df)
            df = categorizer.categorize_transactions(df)
            df = anomaly_detector.detect_anomalies(df)

            # Update global processed data
            processed_data = df

            return jsonify({
                'success': True,
                'message': f'Loaded {len(df)} transactions from Plaid',
                'transaction_count': len(df)
            })
        else:
            return jsonify({
                'success': True,
                'message': 'No transactions found',
                'transaction_count': 0
            })

    except Exception as e:
        return jsonify({'error': f'Failed to get transactions: {str(e)}'}), 500

if __name__ == '__main__':
    # Load data on startup
    print("Loading and processing data...")
    load_and_process_data()

    if processed_data is not None:
        print(f"Successfully loaded {len(processed_data)} transactions")
    else:
        print("Warning: No data loaded. Please ensure your_bank_data.csv exists.")

    # Run the Flask app
    app.run(debug=True, host='0.0.0.0', port=5000)
