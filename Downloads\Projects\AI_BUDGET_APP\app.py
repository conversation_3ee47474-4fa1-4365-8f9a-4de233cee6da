from flask import Flask, render_template, jsonify, request
from flask_cors import CORS
import pandas as pd
import numpy as np
import os
from datetime import datetime, timedelta
import json
from dotenv import load_dotenv

# Import our custom modules
from models.categorizer import TransactionCategorizer
from models.forecaster import SpendingForecaster
from models.anomaly_detector import AnomalyDetector

# Load environment variables
load_dotenv()

app = Flask(__name__)
CORS(app)
app.secret_key = os.getenv('SECRET_KEY', 'your-secret-key-here')

# Initialize our AI models
categorizer = TransactionCategorizer()
forecaster = SpendingForecaster()
anomaly_detector = AnomalyDetector()

# Global variable to store processed data
processed_data = None

def load_and_process_data():
    """Load and process the bank data"""
    global processed_data
    
    data_file = os.getenv('DATA_FILE', 'your_bank_data.csv')
    
    if not os.path.exists(data_file):
        return None
    
    try:
        # Load the CSV data
        df = pd.read_csv(data_file)
        
        # Clean and process the data
        df = clean_transaction_data(df)
        
        # Categorize transactions
        df = categorizer.categorize_transactions(df)
        
        # Detect anomalies
        df = anomaly_detector.detect_anomalies(df)
        
        processed_data = df
        return df
        
    except Exception as e:
        print(f"Error loading data: {e}")
        return None

def clean_transaction_data(df):
    """Clean and standardize transaction data"""
    # Convert date column
    df['Date'] = pd.to_datetime(df['Date'], errors='coerce')
    
    # Handle missing values
    df['Debit'] = pd.to_numeric(df['Debit'], errors='coerce').fillna(0)
    df['Credit'] = pd.to_numeric(df['Credit'], errors='coerce').fillna(0)
    df['Balance'] = pd.to_numeric(df['Balance'], errors='coerce')
    
    # Calculate amount (negative for debits, positive for credits)
    df['Amount'] = df['Credit'] - df['Debit']
    
    # Clean description
    df['Description'] = df['Description'].fillna('Unknown')
    df['Memo'] = df['Memo'].fillna('')
    
    # Sort by date
    df = df.sort_values('Date').reset_index(drop=True)
    
    return df

@app.route('/')
def dashboard():
    """Main dashboard page"""
    return render_template('index.html')

@app.route('/api/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'data_loaded': processed_data is not None,
        'total_transactions': len(processed_data) if processed_data is not None else 0
    })

@app.route('/api/transactions')
def get_transactions():
    """Get all transactions"""
    if processed_data is None:
        load_and_process_data()
    
    if processed_data is None:
        return jsonify({'error': 'No data available'}), 404
    
    # Convert to JSON-serializable format
    transactions = processed_data.copy()
    transactions['Date'] = transactions['Date'].dt.strftime('%Y-%m-%d')
    
    return jsonify({
        'transactions': transactions.to_dict('records'),
        'total': len(transactions)
    })

@app.route('/api/categories')
def get_categories():
    """Get spending analysis by category"""
    if processed_data is None:
        load_and_process_data()
    
    if processed_data is None:
        return jsonify({'error': 'No data available'}), 404
    
    # Calculate spending by category (only debits/expenses)
    expenses = processed_data[processed_data['Amount'] < 0].copy()
    expenses['Amount'] = expenses['Amount'].abs()  # Make positive for display
    
    category_spending = expenses.groupby('Category')['Amount'].sum().sort_values(ascending=False)
    
    return jsonify({
        'categories': category_spending.to_dict(),
        'total_spending': category_spending.sum(),
        'category_count': len(category_spending)
    })

@app.route('/api/forecast')
def get_forecast():
    """Get 30-day spending forecast"""
    if processed_data is None:
        load_and_process_data()
    
    if processed_data is None:
        return jsonify({'error': 'No data available'}), 404
    
    try:
        forecast_data = forecaster.generate_forecast(processed_data)
        return jsonify(forecast_data)
    except Exception as e:
        return jsonify({'error': f'Forecast generation failed: {str(e)}'}), 500

@app.route('/api/anomalies')
def get_anomalies():
    """Get detected anomalies"""
    if processed_data is None:
        load_and_process_data()
    
    if processed_data is None:
        return jsonify({'error': 'No data available'}), 404
    
    # Get anomalous transactions
    anomalies = processed_data[processed_data['is_anomaly'] == True].copy()
    anomalies['Date'] = anomalies['Date'].dt.strftime('%Y-%m-%d')
    
    return jsonify({
        'anomalies': anomalies.to_dict('records'),
        'total_anomalies': len(anomalies),
        'anomaly_rate': len(anomalies) / len(processed_data) * 100
    })

@app.route('/api/stats')
def get_stats():
    """Get overall financial statistics"""
    if processed_data is None:
        load_and_process_data()
    
    if processed_data is None:
        return jsonify({'error': 'No data available'}), 404
    
    df = processed_data
    
    # Calculate key statistics
    total_income = df[df['Amount'] > 0]['Amount'].sum()
    total_expenses = abs(df[df['Amount'] < 0]['Amount'].sum())
    net_change = total_income - total_expenses
    current_balance = df['Balance'].iloc[-1] if len(df) > 0 else 0
    
    # Monthly statistics
    df['Month'] = df['Date'].dt.to_period('M')
    monthly_expenses = df[df['Amount'] < 0].groupby('Month')['Amount'].sum().abs()
    avg_monthly_spending = monthly_expenses.mean() if len(monthly_expenses) > 0 else 0
    
    return jsonify({
        'current_balance': current_balance,
        'total_income': total_income,
        'total_expenses': total_expenses,
        'net_change': net_change,
        'avg_monthly_spending': avg_monthly_spending,
        'transaction_count': len(df),
        'date_range': {
            'start': df['Date'].min().strftime('%Y-%m-%d') if len(df) > 0 else None,
            'end': df['Date'].max().strftime('%Y-%m-%d') if len(df) > 0 else None
        }
    })

if __name__ == '__main__':
    # Load data on startup
    print("Loading and processing data...")
    load_and_process_data()
    
    if processed_data is not None:
        print(f"Successfully loaded {len(processed_data)} transactions")
    else:
        print("Warning: No data loaded. Please ensure your_bank_data.csv exists.")
    
    # Run the Flask app
    app.run(debug=True, host='0.0.0.0', port=5000)
